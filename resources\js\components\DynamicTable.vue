<template>
	<div>
		<label class="mb-1">
			{{ fieldItem.label }}
		</label>
		<table class="table table-borderless table-job-add">
			<thead>
				<tr>
					<th v-for="(field_children, fieldIndex) in converFormFields(fieldItem.childrens, true)" :key="fieldIndex">
						{{ field_children.label }}
						<span v-if="field_children.validation" class="text-danger">*</span>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(itemChildren, itemIndex) in formattedFormulaChildrenResults(fieldItem.name)" :key="itemIndex">
					<td 
						v-for="(field_children, fieldIndex) in converFormFields(fieldItem.childrens, true)" 
						:key="fieldIndex" 
						:class="['col', `col-md-${field_children.column_width}`, `column-width-td`]"
					>
						<div v-if="field_children.type === 'MULTISELECT'">
							<Field 
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="field_children.multiple ? 'tags' : 'single'"
									v-model="itemChildren[field_children.name]"
									:placeholder="field_children.placeholder"
									:close-on-select="false"
									:searchable="true"
									:disabled="field_children.disabled"
									:options="field_children.options"
									:can-clear="field_children.validation ? false : true"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								class="text-danger"
							/>
						</div>
						<div v-else-if="field_children.type === 'USER'">
							<Field 
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="field_children.multiple ? 'tags' : 'single'"
									v-model="itemChildren[field_children.name]"
									:placeholder="field_children.placeholder"
									:close-on-select="false"
									:filter-results="false"
									:resolve-on-load="false"
									:infinite="true"
									:limit="10"
									:clear-on-search="true"
									:searchable="true"
									:delay="0"
									:min-chars="0"
									:object="true"
									:disabled="field_children.disabled"
									:options="getOptionUsers"
									:can-clear="field_children.validation ? false : true"
									@open="getOptionUsers('')"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								class="text-danger"
							/>
						</div>
						<div v-else-if="field_children.type === 'DEPARTMENT'">
							<Field 
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="field_children.multiple ? 'tags' : 'single'"
									v-model="itemChildren[field_children.name]"
									:placeholder="field_children.placeholder"
									:close-on-select="false"
									:searchable="true"
									:object="true"
									:disabled="field_children.disabled"
									:options="selectOptionDepartments"
									:can-clear="field_children.validation ? false : true"
								>
									<template v-slot:option="{ option }">
										<div class="custom-option">
											<div class="option-label mb-1">
												{{ option.label }}
											</div>
											<div class="option-description text-secondary">
												<small>
													<i>{{ option.type }}</i>
												</small>
											</div>
										</div>
									</template>
								</Multiselect>
							</Field>
							<ErrorMessage
								as="div"
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								class="text-danger"
							/>
						</div>
						<div v-else-if="field_children.type === 'FILEUPLOAD'">
							<Field 
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								v-model="itemChildren[field_children.name]"
							>
								<FilePond
									:files="itemChildren[field_children.name]"
									@updatefiles="(fileItemUploads) => $emit('update-file-childrens', fileItemUploads, itemChildren, field_children.name)"
									className="file-pond-children"
									:labelIdle="$t('validate_field.file_upload.label_idle_children')"
									:allowMultiple="field_children.multiple"
									:maxFiles="maxFiles"
									:maxFileSize="maxFileSize"
									:acceptedFileTypes="acceptedFileTypes"
									:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
									:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
									:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
									:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
									:instantUpload="false"
									:name="`itemChildrens[${itemIndex}].${field_children.name}`"
									:ref="`itemChildrens[${itemIndex}].${field_children.name}`"
									credits="false"
									allow-reorder="true"
									item-insert-location="after"
									image-preview-min-height="60"
									image-preview-max-height="60"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								class="text-danger"
							/>
						</div>
						<div v-else-if="field_children.type === 'FORMULA'">
							<FormKit
								type="text"
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								v-model="itemChildren[field_children.name]"
								disabled="false"
							/>
						</div>
						<div v-else-if="field_children.type === 'OBJECTSYSTEM'">
							<div class="d-flex gap-2">
								<div :class="[`col`, `column-width-td`]">
									<Field 
										:name="`itemChildrens[${itemIndex}].${field_children.name}`"
										v-slot="{ field }"
									>
										<Multiselect
											v-bind="field"
											:mode="field_children.multiple ? 'tags' : 'single'"
											v-model="itemChildren[field_children.name]"
											:placeholder="field_children.placeholder"
											:close-on-select="false"
											:filter-results="false"
											:resolve-on-load="false"
											:infinite="true"
											:limit="10"
											:clear-on-search="true"
											:searchable="true"
											:delay="0"
											:min-chars="0"
											:object="true"
											:disabled="field_children.disabled"
											:options="(query) => getOptionColumnData(query, field_children)"
											:can-clear="field_children.validation ? false : true"
											@change="$emit('show-sub-column-table-children', $event, itemIndex, field_children.name)"
											@open="getOptionColumnData('', field_children)"
										/>
									</Field>
									<ErrorMessage
										as="div"
										:name="`itemChildrens[${itemIndex}].${field_children.name}`"
										class="text-danger"
									/>
								</div>
								<div class="d-flex gap-2" v-if="subColumnTableDescriptionChildren[field_children.name] && subColumnTableDescriptionChildren[field_children.name][itemIndex]">
									<div :class="[`col`, `column-width-td`]" class="disabled-column-table-description" v-for="(subColumnTableDescriptionChildren, keyNameChildrenSubColumnTable) in subColumnTableDescriptionChildren[field_children.name][itemIndex]" :key="keyNameChildrenSubColumnTable">
										<FormKit 
											type="text"  
											:label="subColumnTableDescriptionChildren"
											:floating-label="true"
											v-model="subColumnTableOptionSelectedChildren[field_children.name][itemIndex][keyNameChildrenSubColumnTable]" 
										/>
									</div>
								</div>
							</div>
						</div>
						<div v-else>
							<FormKit
								:type="field_children.type"
								:name="`itemChildrens[${itemIndex}].${field_children.name}`"
								:validation="field_children.validation"
								:validation-messages="field_children.validationMessages"
								:class="field_children.class"
								:placeholder="field_children.placeholder"
								v-model="itemChildren[field_children.name]"
								:disabled="field_children.disabled"
							/>
						</div>
					</td> 
					<td class="col-md-1">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							height="24px"
							viewBox="0 -960 960 960"
							width="24px"
							fill="#83868C"
							class="cursor-pointer mt-2"
							@click="$emit('remove-item', fieldItem, itemIndex)"
							v-if="itemIndex !== 0"
						>
							<path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
						</svg>
					</td>
				</tr>
			</tbody>
		</table>
		<button 
			type="button" 
			class="btn btn-primary ms-2 d-flex align-items-center" 
			@click="$emit('add-item', fieldItem)"
		>
			<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
				<path d="M440-280h80v-160h160v-80H520v-160h-80v160H280v80h160v160Zm40 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/>
			</svg>
		</button>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { Field, ErrorMessage } from 'vee-validate'
import Multiselect from '@vueform/multiselect'
import vueFilePond from 'vue-filepond'
import 'filepond/dist/filepond.min.css'
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css'
import FilePondPluginImagePreview from 'filepond-plugin-image-preview'
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type'
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size'
import useDynamicFormLogic from '@/composables/useDynamicFormLogic'

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize)

export default defineComponent({
	name: 'DynamicTable',
	components: {
		Field,
		ErrorMessage,
		Multiselect,
		FilePond
	},
	props: {
		fieldItem: {
			type: Object as () => any,
			required: true
		},
		itemChildrens: {
			type: Object as () => any,
			required: true
		},
		selectOptionDepartments: {
			type: Array as () => any[],
			required: true
		},
		subColumnTableDescriptionChildren: {
			type: Object as () => any,
			required: true
		},
		subColumnTableOptionSelectedChildren: {
			type: Object as () => any,
			required: true
		},
		maxFiles: {
			type: Number,
			required: true
		},
		maxFileSize: {
			type: String,
			required: true
		},
		acceptedFileTypes: {
			type: Array as () => string[],
			required: true
		},
		getOptionUsers: {
			type: Function,
			required: true
		},
		getOptionColumnData: {
			type: Function,
			required: true
		},
		formData: {
			type: Object as () => any,
			required: true
		}
	},
	emits: [
		'update-file-childrens',
		'add-item',
		'remove-item',
		'show-sub-column-table-children'
	],
	setup(props) {
		const { converFormFields, formattedFormulaChildrenResults } = useDynamicFormLogic()

		return {
			converFormFields,
			formattedFormulaChildrenResults: (nameKey: string) => formattedFormulaChildrenResults(nameKey, props.itemChildrens, props.formData)
		}
	}
})
</script>

<style scoped>
.cursor-pointer {
	cursor: pointer;
}
.column-width-td {
	min-width: 200px !important;
}
.disabled-column-table-description {
	pointer-events: none;
}
</style>
