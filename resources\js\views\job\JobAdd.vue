<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-4">
				<FormKit
					ref="dynamicForm"
					type="form"
					:actions="false"
					incomplete-message=" "
					@submit="handleSubmitForm"
				>
					<Form ref="form" @submit="handleSubmitForm" :validation-schema="schema(sortedFormFields)">
						<CCardBody>
								<div class="mb-4">
									<CCol :xs="6">
										<FormKit
											type="checkbox"
											:label="$t('job.is_multiple')"
											name="is_multiple"
											v-model="state.formDataJob.is_multiple"
										/>
									</CCol>
									<CCol :xs="6">
										<FormKit
											type="text"
											:label="$t('job.name')"
											:floating-label="true"
											name="name"
											v-model="state.formDataJob.name"
											validation="required|length:1,200"
											:validation-messages="{
												required: `${$t('job.name')} ${$t('form.validate.required')}`,
												length: `${$t('job.name')} ${$t('form.validate.name_length')}`
											}" 
										/>
									</CCol>
									<CCol :xs="6" class="mb-3">
										<label class="mb-2">
											{{ $t('job.workflow') }}
											<span class="text-danger">*</span>
										</label>
										<Field 
											name="workflow"
											v-slot="{ field }"
										>
											<Multiselect
												v-bind="field"
												v-model="state.formDataJob.workflow"
												:placeholder="$t('job.workflow')"
												:close-on-select="false"
												:filter-results="false"
												:resolve-on-load="false"
												:infinite="true"
												:limit="10"
												:clear-on-search="true"
												:searchable="true"
												:delay="0"
												:min-chars="0"
												:object="true"
												:options="debouncedGetOptionWorkflows"
												@change="changeOptionWorkflow($event)"
												@open="debouncedGetOptionWorkflows('')"
												:can-clear="false"
												class="select-workflow"
											/>
										</Field>
										<ErrorMessage
											as="div"
											name="workflow"
											class="text-danger"
										/>
									</CCol>
									<CCol :xs="6" class="mb-3">
										<div class="d-flex align-items-center">
											<label class="mb-2">
												{{ $t('job.job_manager') }}
												<span class="text-danger">*</span>
											</label>
											<span 
												class="material-symbols-outlined ms-1 cursor-pointer icon-info"
												v-b-tooltip.hover
												:title="$t('workflow.job_manager_desc')"
											>
												info
											</span>
										</div>
										<Field 
											name="job_manager"
												v-slot="{ field }"
											>
											<Multiselect
												mode="tags"
												v-model="state.formDataJob.job_manager"
												v-bind="field"
												:placeholder="$t('job.job_manager')"
												:object="true"
												:disabled="true"
											/>
										</Field>
										<ErrorMessage
											as="div"
											name="job_manager"
											class="text-danger"
										/>
									</CCol>
									<CCol :xs="6" class="mb-3">
										<label class="mb-1">
											{{ $t('job.followers') }}
										</label>
										<Multiselect
											mode="tags"
											v-model="state.formDataJob.followers"
											:placeholder="$t('job.followers')"
											:close-on-select="false"
											:filter-results="false"
											:resolve-on-load="false"
											:infinite="true"
											:limit="20"
											:clear-on-search="true"
											:searchable="true"
											:delay="0"
											:min-chars="0"
											:object="true"
											:options="debouncedGetOptionScopes"
											@open="debouncedGetOptionScopes('')"
											:can-clear="false"
										>
											<template v-slot:option="{ option }">
												<div class="custom-option">
													<div class="option-label mb-1">
														{{ option.label }}
													</div>
													<div class="option-description text-secondary">
														<small>
															<i>{{ option.description }}</i>
														</small>
													</div>
												</div>
											</template>
										</Multiselect>
									</CCol>
								</div>
								<!-- Dynamic Form Fields Component -->
								<DynamicFormFields
									:form-fields="sortedFormFields"
									:form-data="state.formData"
									:item-childrens="state.itemChildrens"
									:select-option-departments="state.selectOptionDepartments"
									:sub-column-table-description="state.subColumnTableDescription"
									:sub-column-table-option-selected="state.subColumnTableOptionSelected"
									:sub-column-table-description-children="state.subColumnTableDescriptionChildren"
									:sub-column-table-option-selected-children="state.subColumnTableOptionSelectedChildren"
									:formatted-formula-results="formattedFormulaResults"
									:max-files="state.maxFiles"
									:max-file-size="state.maxFileSize"
									:accepted-file-types="state.acceptedFileTypes"
									:get-option-users="debouncedGetOptionUsers"
									:get-option-column-data="debouncedGetOptionColumnData"
									@update-form-data="(key, value) => state.formData[key] = value"
									@update-files="updateFiles"
									@update-file-childrens="updateFileChildrens"
									@add-item="addItem"
									@remove-item="removeItem"
									@show-sub-column-table="showSubColumnTable"
									@show-sub-column-table-children="showSubColumnTableChildren"
								/>
								<div class="mb-3">
									<CCol :xs="6">
										<FormKit
											type="textarea"
											:label="$t('job.description')"
											:floating-label="true"
											name="description"
											v-model="state.formDataJob.description"
										/>
									</CCol>
									<CCol :xs="6">
										<label class="mb-1">
											{{ $t('job.files') }}
										</label>
										<FilePond
											:files="state.formDataJob.files"
											@updatefiles="(fileItemUploads) => updateFileDefaults(fileItemUploads)"
											@addfile="onAddFiles"
											className="file-pond"
											:labelIdle="$t('validate_field.file_upload.label_idle')"
											:allowMultiple="true"
											:maxFiles="state.maxFiles"
											:maxFileSize="state.maxFileSize"
											:acceptedFileTypes="state.acceptedFileTypes"
											:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
											:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
											:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
											:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
											:instantUpload="false"
											name="files"
											ref="files"
											credits="false"
											allow-reorder="true"
											item-insert-location="after"
											image-preview-min-height="60"
											image-preview-max-height="60"
										/>
									</CCol>
								</div>
						</CCardBody>
						<CCardFooter>
							<div class="d-flex justify-content-start">
								<CButton 
									type="submit"
									class="btn btn-primary m-1"
									@click="submitForm"
								>
									<span class="text-uppercase">
										{{ $t('job.save_update') }}
									</span>
								</CButton>
								<CButton 
									type="button"
									class="btn btn-light border m-1"
									@click="closeForm"
								>
									<span class="text-uppercase">
										{{ $t('job.close') }}
									</span>
								</CButton>
							</div>
						</CCardFooter>
					</Form>
				</FormKit>
			</CCard>
		</CCol>
	</CRow>
	<loading
		:isLoading="setIsLoading"
	/>
</template>

<script lang="ts">
import { defineComponent, reactive , ref, computed, onMounted } from 'vue'
import { useI18n } from "vue-i18n";
import { useRouter } from 'vue-router';
import Multiselect from '@vueform/multiselect';
import Loading from '@/views/loading/Loading.vue'
import DynamicFormFields from '@/components/DynamicFormFields.vue'
import vueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import  { numberCommas } from "@/utils/utils";
import useFields from '@/composables/field';
import useForms from '@/composables/form';
import useJobs from '@/composables/job';
import useOptions from '@/composables/option';
import useDynamicFormLogic from '@/composables/useDynamicFormLogic';
import debounce from 'lodash.debounce';
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import { useToast } from 'vue-toast-notification';
import { WORKFLOWS } from "@/constants/constants";

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize);
  
export default defineComponent({
    name: "Job",

	components: {
        Multiselect,
		FilePond,
		Form,
		Field,
		ErrorMessage,
		Loading,
		DynamicFormFields
    },
  
    setup() {
		const { t }  = useI18n();
		const $toast = useToast();
		const dynamicForm: any = ref(null);
		const router = useRouter();
		const { 
			converFormFields, 
			formattedFormulaChildrenResults, 
			calculateFormulaWithFormData, 
			sumColumn, 
			initializeItem, 
			initializeItemChildrens 
		} = useDynamicFormLogic();

		const schemaX = (field: any) => {
            let schemaForm = yup.object().shape({});

			const itemChildrenShape = {};
			const itemShape = {};
			
			schemaForm = schemaForm.shape({
				workflow: yup.object()
					.required(`${t('job.workflow')} ${t('form.validate.required')}`),
			});
			
			field.forEach((item: any) => {
				if (item.type === 'TABLE') {
					// Sử dụng state.itemChildrens trực tiếp thay vì formattedFormulaChildrenResults
					const tableItems = state.itemChildrens[item.name] || [];
					tableItems.forEach((itemChildren: any) => {
						const field_childrens = converFormFields(item.childrens, true);
						field_childrens.forEach((item_children: any) => {
							if (['OBJECTSYSTEM', 'USER', 'DEPARTMENT'].includes(item_children.type)) {
								if (item_children.validation && !item_children.value || item_children.validation && itemChildren[item_children.name]?.length === 0) {
									const validationRule = item_children.multiple
										? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
										: yup.object().required(`${item_children.validationMessages.required}`);

									itemChildrenShape[item_children.name] = validationRule;
								}
							}

							if (item_children.type === 'FILEUPLOAD') {
								if (item_children.validation) {
									const validationRule = yup.array().required(`${item_children.validationMessages.required}`);

									itemChildrenShape[item_children.name] = validationRule;
								}
							}

							if (item_children.type === 'MULTISELECT') {
								if (item_children.validation && !item_children.value || item_children.validation && itemChildren[item_children.name]?.length === 0) {
									const validationRule = item_children.multiple
										? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
										: yup.string().required(`${item_children.validationMessages.required}`);

									itemChildrenShape[item_children.name] = validationRule;
								}
							}
						});
					});
				}

				if (item.type === 'FILEUPLOAD') {
					if (item.validation) {
						const validationRule = yup.array().required(`${item.validationMessages.required}`);
						itemShape[item.name] = validationRule;
					}
				}

				if (item.type === 'MULTISELECT') {
					if (item.validation && !item.value || item.validation && item.value.length === 0) {
						const validationRule = item.multiple
							? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
							: yup.string().required(`${item.validationMessages.required}`);

						itemShape[item.name] = validationRule;
					}
				}

				if (['OBJECTSYSTEM', 'USER', 'DEPARTMENT'].includes(item.type)) {
					if (item.validation && !item.value || item.validation && item.value.length === 0) {
						const validationRule = item.multiple
							? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
							: yup.object().required(`${item.validationMessages.required}`);

						itemShape[item.name] = validationRule;
					}
				}
			});

			schemaForm = schemaForm.shape({
				itemChildrens: yup.array().of(
					yup.object().shape(itemChildrenShape)
				),
				...itemShape
			});
			
            return schemaForm;
        }

		const schema = (field: any[]) => {
			// Xây dựng schema cho các trường chính
			let schemaForm = yup.object().shape({});

			schemaForm = schemaForm.shape({
				workflow: yup.object()
					.required(`${t('job.workflow')} ${t('form.validate.required')}`),
				job_manager: yup.array()
					.when('workflow', {
						is: (workflow) => !!workflow,
						then: (schema) => schema
							.min(1, `${t('job.job_manager')} ${t('form.validate.required')}`)
							.required(`${t('job.job_manager')} ${t('form.validate.required')}`),
						otherwise: (schema) => schema.nullable(), // Cho phép job_manager là null hoặc mảng rỗng
					}),
				});

			// Xây dựng các schema cho các trường chính dựa trên `field`
			const itemShape: { [key: string]: any } = {};

			field.forEach((item: any) => {
				// Xử lý các trường không phải là TABLE
				if (item.type !== 'TABLE') {
					if (item.validation && item.disabled == false) {
						switch (item.type) {
							case 'FILEUPLOAD':
									const validationRule = yup.array().required(`${item.validationMessages.required}`);
									itemShape[item.name] = validationRule;
								break;
							case 'MULTISELECT':
								if (!item.value || item.value?.length === 0) {
									const validationRule = item.multiple
										? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
										: yup.string().required(`${item.validationMessages.required}`);
									itemShape[item.name] = validationRule;
								}
								break;
							case 'OBJECTSYSTEM':
							case 'USER':
							case 'DEPARTMENT':
								if (!item.value || item.value?.length === 0) {
									const validationRule = item.multiple
										? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
										: yup.object().required(`${item.validationMessages.required}`);
									itemShape[item.name] = validationRule;
								}
								break;
							default:
								itemShape[item.name] = yup.mixed();
						}
					}
				}
			});

			// Xây dựng schema cho `itemChildrens` sử dụng `yup.lazy`
			schemaForm = schemaForm.shape({
				itemChildrens: yup.array().of(
					yup.lazy((itemChildren: any) => {
						const itemChildrenShape: { [key: string]: any } = {};

						field.forEach((item: any) => {
							if (item.type === 'TABLE') {
								// Lấy các trường con của TABLE từ `fieldItem.childrens`
								const field_childrens = converFormFields(item.childrens, true);
								field_childrens.forEach((item_children: any) => {
									if (item_children.validation && item_children.disabled == false) {
										switch (item_children.type) {
											case 'FILEUPLOAD':
												const validationRule = yup.array().required(`${item_children.validationMessages.required}`);
												itemChildrenShape[item_children.name] = validationRule;
												break;
											case 'MULTISELECT':
												if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
													const validationRule = item_children.multiple
														? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
														: yup.string().required(`${item_children.validationMessages.required}`);
													itemChildrenShape[item_children.name] = validationRule;
												}
												break;
											case 'OBJECTSYSTEM':
											case 'USER':
											case 'DEPARTMENT':
												if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
													const validationRule = item_children.multiple 
														? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
														: yup.object().required(`${item_children.validationMessages.required}`);
													itemChildrenShape[item_children.name] = validationRule;
												}
												break;
											default:
												itemChildrenShape[item_children.name] = yup.mixed();
										}
									}
								});
							}
						});

						return yup.object().shape(itemChildrenShape);
					})
				),
				...itemShape
			});

			return schemaForm;
		};

		const state = reactive({
			maxFiles: 50,
			maxFileSize: '5MB',
			acceptedFileTypes: [
				'image/*',
				'application/pdf', 
				'application/msword', 
				'application/vnd.ms-excel', 
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'text/plain' 
			],
			formDataJob: {
				is_multiple: false,
				name: '',
				workflow: {},
				description: '',
				job_manager: [],
				followers: [],
				files: [],
			} as any,
			formData: {} as any,
            itemChildrens: {} as { [key: string]: any[] },
			dataFormFields: [] as Array<any>,
			selectOptionDepartments: [] as Array<any>,
			subColumnTableDescription: {} as { [key: string]: any },
			subColumnTableOptionSelected: {} as { [key: string]: any },
			subColumnTableDescriptionChildren: {} as { [key: string]: { [index: number]: any } },
			subColumnTableOptionSelectedChildren: {} as { [key: string]: { [index: number]: any } },
			selectOptionSystemDefaults: [
				{ label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
			] as Array<any>,
		});

		const sortedFormFields = computed(() => {
			const transformedFields = converFormFields(state.dataFormFields, false);

			// Initialize itemChildrens for TABLE fields using helper function
			initializeItemChildrens(transformedFields, state.itemChildrens);

			return transformedFields;
		});

		const { getUsers, getDepartments, getWorkflows, getScopes, getUserByOptionScopes } = useOptions();

		const getOptionWorkflows = async (query: string) => {
			try {
				let result = await getWorkflows(query);
				if (Array.isArray(result) && result.length > 0) {
	                return result.map((elem: any) => (
	                    {
	                        value: elem.id,
	                        label: elem.name,
							formId: elem.form_id,
							jobManager: elem.job_manager,
							followers: elem.followers,
	                    }
	                ));
	            }
	            return []; // Luôn trả về array
			} catch (error) {
				console.error('Error fetching workflows:', error);
				return [];
			}
		}

		const debouncedGetOptionWorkflows = debounce(getOptionWorkflows, 500);

		const getOptionUsers = async (query: string) => {
			try {
				let result = await getUsers(query);
				if (Array.isArray(result) && result.length > 0) {
	                return result.map((elem: any) => (
	                    {
	                        value: elem.id,
	                        label: `${elem.account_name} - ${elem.full_name}`,
	                    }
	                ));
	            }
	            return []; // Luôn trả về array
			} catch (error) {
				console.error('Error fetching users:', error);
				return [];
			}
		}

		const debouncedGetOptionUsers = debounce(getOptionUsers, 500);

		const getOptionDepartments = async () => {
            let result = await getDepartments();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionDepartments = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
						type: elem.type,
					} 
				));
            }
		}

		const getOptionProcessScopes = async (query: string) => {
			try {
	            let result = await getScopes(query);
	            if (Array.isArray(result) && result.length > 0) {
	                // Kết hợp các option mặc định với kết quả trả về từ API
	                return [...state.selectOptionSystemDefaults, ...result];
	            }

	            // Nếu không có kết quả từ API, chỉ trả về các option mặc định
	            return [...state.selectOptionSystemDefaults];
			} catch (error) {
				console.error('Error fetching scopes:', error);
				return [...state.selectOptionSystemDefaults];
			}
		}

		const debouncedGetOptionScopes = debounce(getOptionProcessScopes, 500);

		const formulaResults = computed(() => {
			const results = {};
			sortedFormFields.value.forEach((field: any) => {
				if (field.type === 'FORMULA') {
					const formula = field.value;
					if (formula.startsWith('=')) {
						results[field.name] = calculateFormulaWithFormData(field.value, results, state.formData);
					} else {
						results[field.name] = sumColumn(field, state.itemChildrens);
					}
				}
			});

			return results;
		});

		const formattedFormulaResults = computed(() => {
			const results = {};
			for (const key in formulaResults.value) {
				results[key] = numberCommas(formulaResults.value[key]);
				state.formData[key] = results[key];
			}
			
			return results;
		});

		// const formattedFormulaChildrenResults = computed(() => {
		// 	const results = {} as any;
		// 	// Duyệt qua từng key trong itemChildrens
		// 	Object.keys(state.itemChildrens).forEach((key) => {
		// 		results[key] = state.itemChildrens[key].map((item: any) => {
		// 		const newItem = { ...item }; // Copy đối tượng để không sửa trực tiếp dữ liệu gốc
		// 		// Duyệt qua từng key-value trong item
		// 		Object.keys(newItem).forEach((fieldKey) => {
		// 			const value = newItem[fieldKey];
		// 			// Kiểm tra nếu value là công thức (bắt đầu bằng '=')
		// 			if (typeof value === 'string' && value.startsWith('=')) {
		// 				const calculatedValue = calculateFormula(value, newItem);
		// 				newItem[fieldKey] = calculatedValue; // Thay thế giá trị đã tính
		// 			}
		// 			if (typeof value === 'string' && value.startsWith('=')) {
		// 				const calculatedValue = calculateFormula(value, newItem);
		// 				newItem[fieldKey] = numberCommas(calculatedValue); // Format sau khi tính toán
		// 			} else if (typeof value === 'number') {
		// 				// Format các giá trị số
		// 				newItem[fieldKey] = numberCommas(value);
		// 			}
		// 		});
				
		// 		return newItem;
		// 		});
		// 	});
		// 	console.log(results);
			
		// 	return results;
		// });

		onMounted( async () => {
			await getOptionDepartments();
			await initializeValues();
		});

		const changeOptionWorkflow = async (workflow: any) => {
			if (!!workflow) {
				state.formDataJob = {
					is_multiple: false,
					name: state.formDataJob.name || '',
					workflow: {},
					description: '',
					job_manager: [],
					followers: [],
					files: [],
				} as any;
				state.formData = {} as any;
				state.itemChildrens = {} as { [key: string]: any[] };
				// state.selectOptionDepartments = [] as Array<any>;
				state.subColumnTableDescription = {} as { [key: string]: any };
				state.subColumnTableOptionSelected = {} as { [key: string]: any };
				state.subColumnTableDescriptionChildren = {} as { [key: string]: { [index: number]: any } };
				state.subColumnTableOptionSelectedChildren = {} as { [key: string]: { [index: number]: any } };
				const formId = workflow.formId;
				const stageId = WORKFLOWS.STAGE.START;
				state.dataFormFields = await getFieldsByFormId(formId, stageId);
				// Hiển thị followers
				if (!!workflow.followers) {
					const followers = workflow.followers;
					const dataOptionProcessScope = await getOptionProcessScopes('');
					const matchedFollowers = dataOptionProcessScope.filter((item) =>
						followers.includes(item.value)
					);
					state.formDataJob.followers = matchedFollowers;
				}
				// Hiển thị jobManager
				if (!!workflow.jobManager) {
					const jobManager = workflow.jobManager;
					const optionScopes = [...jobManager];
					const datajobManager = await getUserByOptionScopes(optionScopes, workflow.value);
					state.formDataJob.job_manager = datajobManager;
				}
			}
		}

		const initializeValues = async () => {
			for (const field of sortedFormFields.value) {
				if (field.value) {
					state.formData[field.name] = field.value;
				}
			}
		};

		const addItem = (field: any) => {
			state.itemChildrens[field.name].push({ ...initializeItem(field.childrens) });
		};

		const removeItem = (field: any, itemIndex: number) => {
			state.itemChildrens[field.name].splice(itemIndex, 1);
		};

		const { getColumnDatas } = useFields();
		const { getFieldsByFormId } = useForms();
		const { setIsLoading, storeJob } = useJobs();

		const showSubColumnTable = (optionSelected: any, keyName: string) => {
			// Reset dữ liệu trước khi cập nhật
			state.subColumnTableDescription[keyName] = {};
			state.subColumnTableOptionSelected[keyName] = {};

			// Kiểm tra optionSelected khác null va đối tượng khác rỗng
			if (Array.isArray(optionSelected) == false) {
				if (optionSelected !== null && Object.keys(optionSelected).length !== 0) {
					state.subColumnTableDescription[keyName] = optionSelected.sub_column_table_description;
					const subColumnTableOptionSelected = optionSelected.sub_column_table_data_options.find(
						(option: any) => option.id === optionSelected.value
					);
					if (subColumnTableOptionSelected) {
						state.subColumnTableOptionSelected[keyName] = subColumnTableOptionSelected;
					}
				}
			}
		}

		const showSubColumnTableChildren = (optionSelected: any, itemIndex: number, keyName: string) => {
			// Khởi tạo nếu chưa tồn tại
			if (!state.subColumnTableDescriptionChildren[keyName]) {
				state.subColumnTableDescriptionChildren[keyName] = {};
			}
			if (!state.subColumnTableOptionSelectedChildren[keyName]) {
				state.subColumnTableOptionSelectedChildren[keyName] = {};
			}

			// Reset dữ liệu trước khi cập nhật
			state.subColumnTableDescriptionChildren[keyName][itemIndex] = {};
			state.subColumnTableOptionSelectedChildren[keyName][itemIndex] = {};

			// Kiểm tra optionSelected có phải là array hay không
			if (Array.isArray(optionSelected) == false) {
				// Kiểm tra optionSelected khác null và đối tượng khác rỗng
				if (optionSelected !== null && Object.keys(optionSelected).length !== 0) {
					state.subColumnTableDescriptionChildren[keyName][itemIndex] = optionSelected.sub_column_table_description;
					const subColumnTableOptionSelectedChildren = optionSelected.sub_column_table_data_options.find(
						(option: any) => option.id === optionSelected.value
					);
					if (subColumnTableOptionSelectedChildren) {
						state.subColumnTableOptionSelectedChildren[keyName][itemIndex] = subColumnTableOptionSelectedChildren;
					}
				}
			}
		}

		const getOptionColumnData = async (query: string, field: any) => {
			try {
				let result = await getColumnDatas(field.object_table, field.sub_column_table, field.column_table, query);

				if (!!result && Array.isArray(result.data_options) && result.data_options.length > 0) {
	                return result.data_options.map((elem: any) => (
						{
							value: elem.id,
							label: elem[field.column_table],
							sub_column_table_description: result.sub_column_table_description,
							sub_column_table_data_options: result.data_options
						}
					));
	            }
	            return []; // Luôn trả về array
			} catch (error) {
				console.error('Error fetching column data:', error);
				return [];
			}
		}

		const debouncedGetOptionColumnData = debounce(getOptionColumnData, 500);

		const updateFiles = (fileItemUploads: any, fieldName: string) => {
			state.formData[fieldName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};
		const updateFileChildrens = (fileItemUploads: any, itemChildren: object, fieldChildrenName: string) => {
			itemChildren[fieldChildrenName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

		const updateFileDefaults = (fileItemUploads: any) => {
			state.formDataJob.files = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

		const onAddFiles = (error: any, file: any) => {
			if (error) {
				state.formDataJob.files = state.formDataJob.files.filter((item: any) => item.name !== file.filename);
				$toast.open({
					message: `${error.main} - ${error.sub}`,
					type: "warning",
					duration: 10000,
					dismissible: true,
					position: "bottom-right",
				});
			}
		};

		const closeForm = () => {
		}

		const submitForm = () => {
			const node = dynamicForm.value.node;
			node.submit();
			handleSubmitForm();
		}

		const handleSubmitForm = async () => {
			if (setIsLoading.value) {
				return;
			}
			const errorMessages = [] as any;
			const nameJob = state.formDataJob.name;
			const workflowJob = state.formDataJob.workflow;
			const managerJob = state.formDataJob.job_manager;
			
			if (typeof nameJob === 'string' && nameJob.trim() === '') {
				errorMessages.push(`${t('job.name')} ${t('form.validate.required')}`);
			}

			if (typeof workflowJob === 'object' && Object.keys(workflowJob).length === 0) {
                errorMessages.push(`${t('job.workflow')} ${t('form.validate.required')}`);
            }

			if (typeof workflowJob === 'object' && Object.keys(workflowJob).length !== 0 && managerJob.length === 0) {
                errorMessages.push(`${t('job.job_manager')} ${t('form.validate.required')}`);
            }
			
			const formID = state.formDataJob.workflow.formId;

			const requiredFormFields = sortedFormFields.value.filter((field: any) => field.validation === 'required');
			requiredFormFields.forEach((field: any) => {
				const fieldValue = state.formData[field.name];
				const isEmpty =
					!fieldValue ||
					(Array.isArray(fieldValue) && fieldValue.length === 0) ||
					(typeof fieldValue === 'string' && fieldValue.trim() === '');
				if (isEmpty) {
					errorMessages.push(`${field.label} ${t('validate_field.display_name.required')}`);
				}
			});
			// Từ sortedFormFields.value lấy cho tôi field type là TABLE, sau đó sẽ kiểm tra từng field trong state.itemChildrens xem có phải validate Không
			const requiredFormFieldTypeTables = sortedFormFields.value.filter((field: any) => field.type === 'TABLE');
			requiredFormFieldTypeTables.forEach((field: any) => {
				const itemChildrens = state.itemChildrens[field.name];
				if (itemChildrens) {
					const fields = converFormFields(field.childrens, true);
					fields.forEach((f: any) => {
						// Kiểm tra f.validation của từng field trong itemChildrens ,chú ý itemChildrens của tôi có nhiều dòng
						itemChildrens.forEach((item: any) => {
							const fieldValue = item[f.name];
							if (f.validation === 'required') {
								const isEmpty =
									fieldValue === null ||
									fieldValue === undefined ||
									(typeof fieldValue === 'string' && fieldValue.trim() === '') ||
									(Array.isArray(fieldValue) && (fieldValue.length === 0 || fieldValue.every(item => item === null || item === undefined || item === '')));
								if (isEmpty) {
									errorMessages.push(`${field.label} - ${f.label} ${t('validate_field.display_name.required')}`);
								}
							}
						});
					});
				}
			});
			
			if (errorMessages.length > 0) {
				// Kết hợp các thông báo lỗi thành một chuỗi, mỗi lỗi trên một dòng
				const combinedMessage = errorMessages.join('<br>');
				$toast.open({
					message: combinedMessage,
					type: "warning",
					duration: 10000,
					dismissible: true,
					position: "bottom-right",
				});
				
				return;
			}

			const node = dynamicForm.value.node
			const isValidFormKit = node && node.context?.state?.valid;

			if (isValidFormKit) {
				let config: object = {
					header : {
						'Content-Type' : 'multipart/form-data'
					}
				}
				// console.log('Form data:', state.formDataJob, state.formData, state.itemChildrens, state.dataFormFields);
				// Từ sortedFormFields.value lọc cho tôi những item có type là "FILEUPLOAD" và lấy name của những item đó
				const keyFileUploads = sortedFormFields.value.filter((field: any) => field.type === 'FILEUPLOAD').map((field: any) => field.name);
				// Từ sortedFormFields.value lọc cho tôi những item có type là "TABLE" và từ item.childrens lấy cho tôi những item_children co type là "FILEUPLOAD" và lấy name của những item_children đó
				const keyFileUploadChildrens = sortedFormFields.value.filter((field: any) => field.type === 'TABLE').flatMap((field: any) => field.childrens.filter((field_child: any) => field_child.type === 'FILEUPLOAD').map((field_child: any) => field_child.keyword));
				let formJobData = new FormData();
				// Từ keyFileUploads hãy lấy tất cả các giá trị trừ state.formData và formJobData.append giúp tôi ghi giá trị vào formData
				keyFileUploads.forEach((key: string) => {
					if (state.formData.hasOwnProperty(key)) {
						state.formData[key].forEach((file: any, index: number) => {
							// formJobData.append(`${key}[${index}]`, file);
							formJobData.append(`key_file_uploads[${key}][${index}]`, file);
						});
					}
				});
				// Hiện tại keyFileUploadChildrens đang chưa những key ở trong state.itemChildrens mà trong state.itemChildrens đang có nhiều key array, vậy làm thế nào để tôi có thể bóc tách các keyFileUploadChildrens theo đúng với các key có trong state.itemChildrens và ở mỗi key array lại có nhiều item
				Object.keys(state.itemChildrens).forEach((keyItemChildrens: string) => {
					keyFileUploadChildrens.forEach((keyFileUploadChildren: string) => {
						const checkKeyFileUploadChildren = state.itemChildrens[keyItemChildrens].some((item: any) => item.hasOwnProperty(keyFileUploadChildren));
						if (checkKeyFileUploadChildren) {
							state.itemChildrens[keyItemChildrens].forEach((item: any, itemIndex: number) => {
								if (item[keyFileUploadChildren]) {
									item[keyFileUploadChildren].forEach((file: any, fileIndex: number) => {
										// formJobData.append(`key_file_upload_childrens_${keyItemChildrens}[${itemIndex}][${keyFileUploadChildren}][${fileIndex}]`, file);
										formJobData.append(`key_file_upload_childrens[${itemIndex}][${keyFileUploadChildren}][${fileIndex}]`, file);
									});
								}
							});
						}
					});
				});
				
				Object.keys(state.itemChildrens).forEach((keyItemChildrens: string) => {
					// formJobData.append(`${keyItemChildrens}`, JSON.stringify(state.itemChildrens[keyItemChildrens]));
					// Tôi muons append vào key table  Tôi muons append  có thể bọc lớp table ở ngoài không
					formJobData.append(`key_tables[${keyItemChildrens}]`, JSON.stringify(state.itemChildrens[keyItemChildrens]));
				});

				if (state.formDataJob.files) {
					state.formDataJob.files.forEach((file: any, index: number) => {
						formJobData.append(`fileUploads[${index}]`, file);
					});
				}

				formJobData.append('formData', JSON.stringify(state.formData));
				formJobData.append('formDataJob', JSON.stringify(state.formDataJob));
				formJobData.append('formId', formID);
				formJobData.append('stageId', WORKFLOWS.STAGE.START);
				formJobData.append('actionId', WORKFLOWS.ACTION.CREATE);
				
				let response: any = await storeJob(formJobData, config);

				if (response && response.status === 'success') {
					$toast.open({
						message: t('toast.status.ACTION_SUCCESS'),
						type: "success",
						duration: 5000,
						dismissible: true,
						position: "bottom-right",
					});

					await router.push({ name: 'JobDetail', params: { id: response.job_id } });
				}
			} else {
				console.warn('Form chưa valid hoặc node không tồn tại, không thể submit!')
			}
		}

		return {
			state,
			schema,
			setIsLoading,
			debouncedGetOptionUsers,
			debouncedGetOptionWorkflows,
			debouncedGetOptionScopes,
			changeOptionWorkflow,
			dynamicForm,
			sortedFormFields,
			formattedFormulaResults,
			formattedFormulaChildrenResults: (nameKey: string) => formattedFormulaChildrenResults(nameKey, state.itemChildrens, state.formData),
			converFormFields,
			addItem,
      		removeItem,
			showSubColumnTable,
			showSubColumnTableChildren,
			debouncedGetOptionColumnData,
			updateFiles,
			updateFileChildrens,
			updateFileDefaults,
			onAddFiles,
			closeForm,
			submitForm,
			handleSubmitForm,
		}
    }
});
</script>

<style scoped>
	.cursor-pointer {
		cursor: pointer;
	}
	.column-width-td {
		min-width: 200px !important;
	}
	.card-footer {
		z-index: 10;
		position: sticky;
		left: 0px;
		bottom: 0px;
		width: 100%;
		background-color:#f8f9fa;
		padding: 10px;
	}
	.select-workflow {
		padding: 1.875px !important;
	}
	.disabled-column-table-description {
		pointer-events: none;
	}
	.icon-info {
		font-size: 16px !important;
		color: #0d6efd !important;
	}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>