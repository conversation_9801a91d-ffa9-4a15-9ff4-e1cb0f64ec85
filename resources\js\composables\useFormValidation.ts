import * as yup from 'yup'
import useDynamicFormLogic from '@/composables/useDynamicFormLogic'

export default function useFormValidation(t: any) {
	const { converFormFields } = useDynamicFormLogic()

	// Create dynamic validation schema
	const createDynamicSchema = (fields: any[]) => {
		// Xây dựng schema cho các trường chính
		let schemaForm = yup.object().shape({});

		schemaForm = schemaForm.shape({
			workflow: yup.object()
				.required(`${t('job.workflow')} ${t('form.validate.required')}`),
			job_manager: yup.array()
				.when('workflow', {
					is: (workflow: any) => !!workflow,
					then: (schema) => schema
						.min(1, `${t('job.job_manager')} ${t('form.validate.required')}`)
						.required(`${t('job.job_manager')} ${t('form.validate.required')}`),
					otherwise: (schema) => schema.nullable(),
				}),
			});

		// Xây dựng các schema cho các trường chính dựa trên `field`
		const itemShape: { [key: string]: any } = {};

		// Safety check for fields
		if (!fields || !Array.isArray(fields)) {
			return schemaForm;
		}

		fields.forEach((item: any) => {
			// Xử lý các trường không phải là TABLE
			if (item.type !== 'TABLE') {
				if (item.validation && item.disabled == false) {
					switch (item.type) {
						case 'FILEUPLOAD':
								const validationRule = yup.array().required(`${item.validationMessages.required}`);
								itemShape[item.name] = validationRule;
							break;
						case 'MULTISELECT':
							if (!item.value || item.value?.length === 0) {
								const validationRule = item.multiple
									? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
									: yup.string().required(`${item.validationMessages.required}`);
								itemShape[item.name] = validationRule;
							}
							break;
						case 'OBJECTSYSTEM':
						case 'USER':
						case 'DEPARTMENT':
							if (!item.value || item.value?.length === 0) {
								const validationRule = item.multiple
									? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
									: yup.object().required(`${item.validationMessages.required}`);
								itemShape[item.name] = validationRule;
							}
							break;
						default:
							itemShape[item.name] = yup.mixed();
					}
				}
			}
		});

		// Xây dựng schema cho `itemChildrens` sử dụng `yup.lazy`
		schemaForm = schemaForm.shape({
			itemChildrens: yup.array().of(
				yup.lazy((itemChildren: any) => {
					const itemChildrenShape: { [key: string]: any } = {};

					fields.forEach((item: any) => {
						if (item.type === 'TABLE') {
							// Lấy các trường con của TABLE từ `fieldItem.childrens`
							const field_childrens = converFormFields(item.childrens, true);
							field_childrens.forEach((item_children: any) => {
								if (item_children.validation && item_children.disabled == false) {
									switch (item_children.type) {
										case 'FILEUPLOAD':
											const validationRule = yup.array().required(`${item_children.validationMessages.required}`);
											itemChildrenShape[item_children.name] = validationRule;
											break;
										case 'MULTISELECT':
											if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
												const validationRule = item_children.multiple
													? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
													: yup.string().required(`${item_children.validationMessages.required}`);
												itemChildrenShape[item_children.name] = validationRule;
											}
											break;
										case 'OBJECTSYSTEM':
										case 'USER':
										case 'DEPARTMENT':
											if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
												const validationRule = item_children.multiple 
													? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
													: yup.object().required(`${item_children.validationMessages.required}`);
												itemChildrenShape[item_children.name] = validationRule;
											}
											break;
										default:
											itemChildrenShape[item_children.name] = yup.mixed();
									}
								}
							});
						}
					});

					return yup.object().shape(itemChildrenShape);
				})
			),
			...itemShape
		});

		return schemaForm;
	};

	// Validate required fields
	const validateRequiredFields = (sortedFormFields: any, formData: any, itemChildrens: any) => {
		const errorMessages = [] as any;

		// Safety check for sortedFormFields
		if (!sortedFormFields || !Array.isArray(sortedFormFields)) {
			return errorMessages;
		}

		const requiredFormFields = sortedFormFields.filter((field: any) => field.validation === 'required');
		requiredFormFields.forEach((field: any) => {
			const fieldValue = formData[field.name];
			const isEmpty =
				!fieldValue ||
				(Array.isArray(fieldValue) && fieldValue.length === 0) ||
				(typeof fieldValue === 'string' && fieldValue.trim() === '');
			if (isEmpty) {
				errorMessages.push(`${field.label} ${t('validate_field.display_name.required')}`);
			}
		});

		// Validate TABLE fields
		const requiredFormFieldTypeTables = sortedFormFields.filter((field: any) => field.type === 'TABLE');
		requiredFormFieldTypeTables.forEach((field: any) => {
			const itemChildrensData = itemChildrens[field.name];
			if (itemChildrensData) {
				const fields = converFormFields(field.childrens, true);
				fields.forEach((f: any) => {
					itemChildrensData.forEach((item: any) => {
						const fieldValue = item[f.name];
						if (f.validation === 'required') {
							const isEmpty =
								fieldValue === null ||
								fieldValue === undefined ||
								(typeof fieldValue === 'string' && fieldValue.trim() === '') ||
								(Array.isArray(fieldValue) && (fieldValue.length === 0 || fieldValue.every(item => item === null || item === undefined || item === '')));
							if (isEmpty) {
								errorMessages.push(`${field.label} - ${f.label} ${t('validate_field.display_name.required')}`);
							}
						}
					});
				});
			}
		});

		return errorMessages;
	}

	return {
		createDynamicSchema,
		validateRequiredFields
	}
}
