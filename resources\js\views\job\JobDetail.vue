<template>
	
	<CCard>
		<CCardHeader>
			<ul class="nav">
				<li v-for="(tab, index) in optionTabs" :key="index" class="nav-item">
					<a 
						:class="{ active: isActiveTab(tab.value) }" 
						class="nav-link"
						@click="handleClickTab(tab.value)"
					>
						{{ tab.title }}
					</a>
				</li>
			</ul>
		</CCardHeader>
		<div v-if="state.tabActived === TAB_JOB_DETAIL.DETAIL">
			<CCol :xs="12">
				<BAccordion flush free>
					<BAccordionItem :title="$t('job.workflow_info')" visible>
						<div class="table-responsive">
							<table class="table table-hover table-bordered workflow-table">
								<thead>
									<tr>
										<th class="align-middle">
											{{ $t('workflow.stage.from_stage') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.action.title') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.stage.to_stage') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.condition.title') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.email.title') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.sync_group.title') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.stage.back_to_stage') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.action.title_back') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.email.title_back') }}
										</th>
										<th class="align-middle">
											{{ $t('workflow.process_transition.title') }}
										</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="(transition, index) in jobDetail?.flow_transitions" :key="index">
										<!-- Giai đoạn bắt đầu -->
										<td @click="transition.can_execute ? showDetailModal('from_stage', transition) : null" 
											:class="{ 'has-data': transition.from_stage?.name, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.from_stage?.name ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.from_stage?.name">
												<span>{{ transition.from_stage.name }}</span>
												<CBadge v-if="transition.from_stage.status" :color="getStatusBadge(transition.from_stage.status).color" class="ms-2">
													{{ getStatusBadge(transition.from_stage.status).text }}
												</CBadge>
											</div>
											<span v-else>{{ $t('common.no_data') }}</span>
										</td>
										
										<!-- Hành động chuyển tiếp -->
										<td @click="transition.can_execute ? showDetailModal('action', transition) : null" 
											:class="{ 'has-data': transition.action?.name, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.action?.name ? $t('workflow.not_executable_tooltip') : ''">
											{{ transition.action?.name || $t('common.no_data') }}
										</td>
										
										<!-- Giai đoạn chuyển tiếp -->
										<td @click="transition.can_execute ? showDetailModal('to_stage', transition) : null" 
											:class="{ 'has-data': transition.to_stage?.name, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.to_stage?.name ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.to_stage?.name">
												<span>{{ transition.to_stage.name }}</span>
												<CBadge v-if="transition.from_stage.status && transition.to_stage.id !== WORKFLOWS.STAGE.DONE && transition.to_stage.id !== WORKFLOWS.STAGE.FALSE" :color="getStatusBadge(transition.to_stage.status).color" class="ms-2">
													{{ getStatusBadge(transition.to_stage.status).text }}
												</CBadge>
											</div>
											<span v-else>{{ $t('common.no_data') }}</span>
										</td>
										
										<!-- Điều kiện chuyển tiếp -->
										<td @click="transition.can_execute ? showDetailModal('conditions', transition) : null" 
											:class="{ 'has-data': transition.conditions && transition.conditions.length, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.conditions && transition.conditions.length ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.conditions && transition.conditions.length">
												<div v-for="(condition, cIdx) in transition.conditions" :key="`c-${cIdx}`">
													{{ condition.name }} ({{ condition.status ? $t('workflow.condition.status.success') : $t('workflow.condition.status.failure') }})
												</div>
											</div>
											<div v-else>{{ $t('common.no_data') }}</div>
										</td>
										
										<!-- Email gửi -->
										<td @click="transition.can_execute ? showDetailModal('email_templates', transition) : null" 
											:class="{ 'has-data': transition.email_templates && transition.email_templates.length, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.email_templates && transition.email_templates.length ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.email_templates && transition.email_templates.length">
												<div v-for="(email, eIdx) in transition.email_templates" :key="`e-${eIdx}`">
													{{ email.name || email.subject || $t('common.no_data') }}
												</div>
											</div>
											<div v-else>{{ $t('common.no_data') }}</div>
										</td>
										
										<!-- Đồng bộ giai đoạn -->
										<td @click="transition.can_execute ? showDetailModal('sync_groups', transition) : null" 
											:class="{ 'has-data': transition.sync_groups && transition.sync_groups.length, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.sync_groups && transition.sync_groups.length ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.sync_groups && transition.sync_groups.length">
												<div v-for="(syncGroup, sgIdx) in transition.sync_groups" :key="`sg-${sgIdx}`">
													{{ syncGroup.name || $t('common.no_data') }}
												</div>
											</div>
											<div v-else>{{ $t('common.no_data') }}</div>
										</td>
										
										<!-- Giai đoạn quay lại -->
										<td @click="transition.can_execute ? showDetailModal('back_to_stage', transition) : null" 
											:class="{ 'has-data': transition.back_to_stage?.name, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.back_to_stage?.name ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.back_to_stage?.name">
												<span>{{ transition.back_to_stage.name }}</span>
											</div>
											<span v-else>{{ $t('common.no_data') }}</span>
										</td>
										
										<!-- Hành động quay lại -->
										<td @click="transition.can_execute ? showDetailModal('action_back_to', transition) : null" 
											:class="{ 'has-data': transition.action_back_to?.name, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.action_back_to?.name ? $t('workflow.not_executable_tooltip') : ''">
											{{ transition.action_back_to?.name || $t('common.no_data') }}
										</td>
										
										<!-- Email quay lại -->
										<td @click="transition.can_execute ? showDetailModal('email_template_back', transition) : null" 
											:class="{ 'has-data': transition.email_template_back && transition.email_template_back.length, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.email_template_back && transition.email_template_back.length ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.email_template_back && transition.email_template_back.length">
												<div v-for="(email, eIdx) in transition.email_template_back" :key="`e-${eIdx}`">
													{{ email.name || email.subject || $t('common.no_data') }}
												</div>
											</div>
											<div v-else>{{ $t('common.no_data') }}</div>
										</td>
										
										<!-- Công việc xử lý tiếp -->
										<td @click="transition.can_execute ? showDetailModal('process_transition', transition) : null" 
											:class="{ 'has-data': transition.process_transition && transition.process_transition.length, 'not-executable': !transition.can_execute }"
											:title="!transition.can_execute && transition.process_transition && transition.process_transition.length ? $t('workflow.not_executable_tooltip') : ''">
											<div v-if="transition.process_transition && transition.process_transition.length">
												<div v-for="(pt, ptIdx) in transition.process_transition" :key="`pt-${ptIdx}`">
													{{ pt.name || $t('common.no_data') }}
												</div>
											</div>
											<div v-else>{{ $t('common.no_data') }}</div>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</BAccordionItem>
				</BAccordion>
			</CCol>
			<CRow>
				<CCol :xs="8">
					<BAccordion flush free>
						<BAccordionItem :title="$t('job.general_info')" visible>
							<CForm class="row g-2">
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.name') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.job_position') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.job_position?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.department') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.department?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.rank') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.assigned_user?.rank?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.manager') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.manager_users?.length ? jobDetail.manager_users.map(user => user.full_name).join(', ') : $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.followers') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.follower_users?.length ? jobDetail.follower_users.map(user => user.full_name).join(', ') : $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.workflow') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.process_version?.process?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.status') }}
									</CFormLabel>
									<div class="fw-normal" v-if="jobDetail?.status">
										<span class="badge rounded-pill bg-info" v-if="jobDetail?.status === SAVE_JOB_STATUS.PENDING">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.pending') }}
											</small>
										</span>
										<span class="badge rounded-pill bg-warning" v-else-if="jobDetail?.status === SAVE_JOB_STATUS.PROCESSING">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.processing') }}
											</small>
										</span>
										<span class="badge rounded-pill bg-success" v-else-if="jobDetail?.status === SAVE_JOB_STATUS.COMPLETED">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.completed') }}
											</small>
										</span>
										<span class="badge rounded-pill bg-danger" v-else-if="jobDetail?.status === SAVE_JOB_STATUS.CANCEL">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.cancel') }}
											</small>
										</span>
									</div>
									<div v-else>
										{{ $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="12" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.description') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.description || $t('common.no_data') }}
									</div>
								</CCol>
							</CForm>
						</BAccordionItem>
						<BAccordionItem :title="$t('job.custom_field')" visible>
							<CForm class="row g-2">
								<template v-for="fieldValue in jobDetail?.job_field_values" :key="fieldValue.id">
									<CCol :md="fieldValue.field?.type === 'TABLE' ? 12 : 6" class="mb-3">
										<CFormLabel class="text-secondary">
											{{ fieldValue?.label }}
										</CFormLabel>
										<div class="fw-normal">
											<template v-if="fieldValue.field?.type === 'FILEUPLOAD'">
												<div v-for="(file, index) in getFileList(fieldValue)" :key="index">
													<a 
														href="#" 
														@click.prevent="handleFileClick(file)"
														class="fw-normal text-decoration-none"
													>
														<p>{{ getFileName(file) }}</p>
													</a>
												</div>
											</template>
											<template v-else-if="fieldValue.field?.type === 'TABLE'">
												<CTable align="middle" responsive>
													<table class="table table-hover custom-table">
														<thead>
															<tr>
																<th class="align-middle" 
																	v-for="(value, key) in fieldValue.table_values[0]" 
																	:key="key"
																	:data-object-system="fieldValue.table_columns_type[key] === 'OBJECTSYSTEM' ? true : undefined"
																>
																	<div class="d-flex">
																		<strong class="object-main-title">{{ key }}</strong>
																		<template v-if="fieldValue.table_columns_type[key] === 'OBJECTSYSTEM' && Array.isArray(value) && value.length > 0 && typeof value[0] === 'object'">
																			<div class="d-flex">
																				<template v-for="(item, key_item) in Object.keys(value[0]).filter(key => key !== 'main')" :key="key_item">
																					<small class="text-muted object-field-item">{{ item }}</small>
																				</template>
																			</div>
																		</template>
																	</div>
																</th>
															</tr>
														</thead>
														<tbody>
															<tr v-for="(row, rowIndex) in fieldValue.table_values" :key="rowIndex">
																<td class="align-middle" 
																	v-for="(value, key) in row" 
																	:key="key"
																	:data-object-system="fieldValue.table_columns_type[key] === 'OBJECTSYSTEM' ? true : undefined"
																>
																	<template v-if="isColumnTypeFileUpload(fieldValue, key)">
																		<div v-for="(file, fileIndex) in getFileArrayFromValue(value)" :key="fileIndex">
																			<a 
																				href="#" 
																				@click.prevent="handleFileClick(file)"
																				class="fw-normal text-decoration-none"
																			>
																				{{ getFileName(file) }}
																			</a>
																		</div>
																	</template>
																	<template v-else>
																		<div class="d-flex">
																			<div class="object-main-title">{{ formatTableValue(value) }}</div>
																			<template v-if="fieldValue.table_columns_type[key] === 'OBJECTSYSTEM' && Array.isArray(value)">
																				<template v-for="(obj, objIndex) in value" :key="objIndex">
																					<div class="d-flex">
																						<template v-for="(val, objKey) in obj" :key="objKey">
																							<template v-if="String(objKey) !== 'main'">
																								<small class="text-muted object-field-item">{{ val }}</small>
																							</template>
																						</template>
																					</div>
																				</template>
																			</template>
																		</div>
																	</template>
																</td>
															</tr>
														</tbody>
													</table>
												</CTable>
											</template>
											<template v-else>
												{{ getFieldValue(fieldValue) }}
											</template>
										</div>
									</CCol>
								</template>
							</CForm>
						</BAccordionItem>
					</BAccordion>
				</CCol>
				<CCol :xs="4">
					<BAccordion flush free>
						<BAccordionItem :title="$t('job.history_action')" visible>
							<div v-if="jobDetail?.job_approval_histories && jobDetail.job_approval_histories.length > 0" class="history-timeline">
								<div
									v-for="(history, index) in filteredHistories"
									:key="history.id"
									class="history-item d-flex mb-3"
								>
									<!-- Icon và đường kết nối -->
									<div class="history-icon-wrapper me-3">
										<div class="history-icon">
											<span :class="`material-symbols-outlined text-white p-1 rounded-circle ${getHistoryAction(history).actionIconClass}`">
												{{ getHistoryAction(history).actionIcon }}
											</span>
										</div>
										<!-- Đường kết nối (không hiển thị cho item cuối) -->
										<div
											v-if="index < jobDetail.job_approval_histories.length - 1"
											class="history-line"
										></div>
									</div>

									<!-- Nội dung lịch sử -->
									<div class="history-content flex-grow-1">
										<div class="history-header d-flex justify-content-between align-items-start">
											<div>
												<strong class="history-actor">
													{{ getHistoryActorName(history) }}
												</strong>
												<span class="history-action text-muted ms-2">
													{{ getHistoryAction(history).actionText }}
												</span>
											</div>
											<small class="text-muted history-time">
												{{ formatHistoryDate(history.updated_at) }}
											</small>
										</div>

										<div class="history-stage mt-2">
											<!-- Ngày -->
											<div v-if="history.date" class="mt-1">
												<span class="text-secondary">
													{{ $t('job.history.date') }}: {{ formatDateTime(history.date) }}
												</span>
											</div>
											<!-- Thông tin giai đoạn -->
											<div v-if="history.stage" class="mt-1">
												<span class="text-secondary">
													{{ $t('job.history.stage') }}: {{ history.stage.name }}
												</span>
											</div>
											<!-- Thông tin hành động -->
											<div v-if="history.action" class="mt-1">
												<span class="text-secondary">
													{{ $t('job.history.action') }}: {{ history.action.name }}
												</span>
											</div>
											<!-- Nhận xét -->
											<div v-if="history.comment" class="comment-text mt-1">
												<span class="text-secondary">
													{{ $t('job.history.comment') }}: {{ history.comment }}
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- Trường hợp không có lịch sử -->
							<div v-else class="text-center text-muted py-4">
								<i class="fas fa-history fa-2x mb-2"></i>
								<div>{{ $t('job.history.no_histossry') }}</div>
							</div>
						</BAccordionItem>
					</BAccordion>
				</CCol>
			</CRow>
		</div>
		<div v-if="state.tabActived === TAB_JOB_DETAIL.FILES">
			<BAccordion flush free>
				<BAccordionItem :title="$t('job.files')" visible>
					<CCol :md="12" class="mb-3">
						<div class="fw-normal">
							<template v-if="jobDetail?.files && jobDetail.files.length > 0">
								<div v-for="(file, index) in jobDetail.files" :key="index">
									<a 
										href="#" 
										@click.prevent="handleFileClick(file)"
										class="fw-normal text-decoration-none"
									>
										<p>{{ getFileName(file) }}</p>
									</a>
								</div>
							</template>
							<template v-else>
								{{ $t('common.no_data') }}
							</template>
						</div>
					</CCol>
				</BAccordionItem>
			</BAccordion>
		</div>
	</CCard>

	<!-- Modal xem file -->
	<CModal
		:visible="state.showFileModal"
		@close="state.showFileModal = false"
		size="xl"
		fullscreen
		class="file-preview-modal"
	>
		<CModalHeader>
			<CModalTitle>{{ state.currentFileName }}</CModalTitle>
		</CModalHeader>
		<CModalBody>
			<div v-if="isPreviewableFile(state.currentFile)" class="text-center">
				<img 
					v-if="isImageFile(state.currentFile)" 
					:src="getFileUrl(state.currentFile)" 
					class="img-fluid"
					alt="Preview"
				/>
				<iframe 
					v-else-if="isPdfFile(state.currentFile)"
					:src="getFileUrl(state.currentFile)"
					width="100%"
					height="5000px"
					frameborder="0"
				></iframe>
			</div>
			<div v-else class="text-center">
				<p>{{ $t('job.file_not_previewable') }}</p>
				<CButton color="primary" @click="downloadFile(state.currentFile)">
					{{ $t('job.download') }}
				</CButton>
			</div>
		</CModalBody>
	</CModal>

	<!-- Modal hiển thị chi tiết -->
	<BModal
		:title="state.detailModalTitle"
		v-model="state.showDetailModal"
		centered
		size="lg"
		scrollable
		hide-footer
		no-close-on-backdrop
        no-close-on-esc
		@close="closeShowDetailModal"
	>
		<div v-if="state.currentDetailType === 'from_stage' && state.currentDetailData">
			<CForm class="row g-2">
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.name')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.from_stage?.name || $t('common.no_data') }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.approver')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ formatApprovers(state.currentDetailData.from_stage?.approvers) }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.follower')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ formatFollowers(state.currentDetailData.from_stage?.followers) }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.description')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.from_stage?.description || $t('common.no_data') }}
					</div>
				</CCol>
			</CForm>
		</div>

		<div v-else-if="state.currentDetailType === 'to_stage' && state.currentDetailData">
			<CForm class="row g-2">
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.name')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.to_stage?.name || $t('common.no_data') }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.approver')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ formatApprovers(state.currentDetailData.to_stage?.approvers) }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.follower')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ formatFollowers(state.currentDetailData.to_stage?.followers) }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.description')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.to_stage?.description || $t('common.no_data') }}
					</div>
				</CCol>
			</CForm>
		</div>

		<div v-else-if="state.currentDetailType === 'action' && state.currentDetailData">
			<FormKit
				ref="dynamicForm"
				type="form"
				:actions="false"
				incomplete-message=" "
				@submit="() => executeAction(state.currentDetailData, 'action')"
			>
				<Form ref="form" @submit="() => executeAction(state.currentDetailData, 'action')" :validation-schema="schema(sortedFormFields)">
					<CForm class="row g-2">
						<CCol :md="6" class="mb-3">
							<CFormLabel class="text-secondary">
								{{ $t('workflow.action.name')}}
							</CFormLabel>
							<div class="fw-normal">
								{{ state.currentDetailData.action?.name || $t('common.no_data') }}
							</div>
						</CCol>
						<CCol :md="6" class="mb-3">
							<CFormLabel class="text-secondary">
								{{ $t('workflow.action.description')}}
							</CFormLabel>
							<div class="fw-normal">
								{{ state.currentDetailData.action?.description || $t('common.no_data') }}
							</div>
						</CCol>
						<CCol :xs="12" class="mb-3">
							<CFormLabel class="text-secondary">
								{{ $t('workflow.action.comment') }}
							</CFormLabel>
							<CFormTextarea
								id="comment"
								v-model="state.comment"
								:placeholder="$t('workflow.action.comment_desc')"
								:maxlength="500"
								rows="2"
								:invalid="!!state.commentError"
								@input="state.commentError = ''"
							></CFormTextarea>
							<CFormFeedback invalid>
								{{ state.commentError }}
							</CFormFeedback>
						</CCol>

						<!-- Dynamic Form Fields -->
						<div v-if="state.dynamicFormLoading" class="text-center mb-3">
							<Loading />
						</div>
						<div v-else-if="state.showDynamicForm && sortedFormFields.length > 0">
							<CCol :xs="12" class="mb-3">
								<hr>
								<h6 class="text-secondary mb-3">{{ $t('form.field.setup') }}</h6>
								<DynamicFormFields
									:form-fields="sortedFormFields"
									:form-data="state.formData"
									:item-childrens="state.itemChildrens"
									:select-option-departments="state.selectOptionDepartments"
									:sub-column-table-description="state.subColumnTableDescription"
									:sub-column-table-option-selected="state.subColumnTableOptionSelected"
									:sub-column-table-description-children="state.subColumnTableDescriptionChildren"
									:sub-column-table-option-selected-children="state.subColumnTableOptionSelectedChildren"
									:formatted-formula-results="formattedFormulaResults"
									:max-files="state.maxFiles"
									:max-file-size="state.maxFileSize"
									:accepted-file-types="state.acceptedFileTypes"
									:get-option-users="getOptionUsersHandler"
									:get-option-column-data="getOptionColumnDataHandler"
									@update-form-data="(name, value) => state.formData[name] = value"
									@add-item="(field) => addItem(field)"
									@remove-item="(field, index) => removeItem(field, index)"
									@show-sub-column-table="(optionSelected, keyName) => showSubColumnTable(optionSelected, keyName)"
									@show-sub-column-table-children="(optionSelected, itemIndex, keyName) => showSubColumnTableChildren(optionSelected, itemIndex, keyName)"
									@update-files="(fileItemUploads, fieldName) => updateFiles(fileItemUploads, fieldName)"
									@update-file-childrens="(fileItemUploads, itemChildren, fieldChildrenName) => updateFileChildrens(fileItemUploads, itemChildren, fieldChildrenName)"
								/>
							</CCol>
						</div>

						<CCardFooter>
							<div class="d-flex justify-content-end">
								<CButton
									type="button"
									class="btn btn-light border m-1"
									@click="closeShowDetailModal"
								>
									<span class="text-uppercase">
										{{ $t('workflow.action.close') }}
									</span>
								</CButton>
								<CButton
									type="button"
									class="btn btn-primary m-1"
									@click="executeAction(state.currentDetailData, 'action')"
								>
									<span class="text-uppercase">
										{{ $t('workflow.action.execute') }}
									</span>
								</CButton>
							</div>
						</CCardFooter>
					</CForm>
				</Form>
			</FormKit>
		</div>

		<div v-else-if="state.currentDetailType === 'conditions' && state.currentDetailData">
			<div class="detail-section">
				<div class="detail-header">{{ $t('workflow.conditions_info') }}</div>
				<div class="detail-content">
					<div v-if="state.currentDetailData.conditions && state.currentDetailData.conditions.length">
						<div v-for="(condition, cIdx) in state.currentDetailData.conditions" :key="`dc-${cIdx}`" class="condition-item mb-3">
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.name') }}:</div>
								<div class="detail-value">{{ condition.name || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.status') }}:</div>
								<div class="detail-value">
									<CBadge :color="condition.status ? 'success' : 'danger'">
										{{ condition.status ? $t('workflow.enabled') : $t('workflow.disabled') }}
									</CBadge>
								</div>
							</div>
							<div v-if="condition.or_conditions && condition.or_conditions.length" class="detail-item">
								<div class="detail-label">{{ $t('workflow.or_conditions') }}:</div>
								<div class="detail-value">
									<div v-for="(orCondition, orIdx) in condition.or_conditions" :key="`or-${orIdx}`" class="mb-1">
										<div class="or-condition-item">
											{{ orCondition.field || orCondition.field_name || $t('common.no_data') }} 
											{{ orCondition.operator || $t('common.no_data') }} 
											{{ orCondition.value || $t('common.no_data') }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">{{ $t('common.no_data') }}</div>
				</div>
			</div>
		</div>

		<div v-else-if="state.currentDetailType === 'email_templates' && state.currentDetailData">
			<div class="detail-section">
				<div class="detail-header">{{ $t('workflow.email_templates_info') }}</div>
				<div class="detail-content">
					<div v-if="state.currentDetailData.email_templates && state.currentDetailData.email_templates.length">
						<div v-for="(email, eIdx) in state.currentDetailData.email_templates" :key="`de-${eIdx}`" class="email-template-item mb-4">
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.template_name') }}:</div>
								<div class="detail-value">{{ email.name || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.subject') }}:</div>
								<div class="detail-value">{{ email.subject || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.from_email') }}:</div>
								<div class="detail-value">{{ email.from_email || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.to_emails') }}:</div>
								<div class="detail-value">{{ Array.isArray(email.to_emails) ? email.to_emails.join(', ') : email.to_emails || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.cc_emails') }}:</div>
								<div class="detail-value">{{ Array.isArray(email.cc_emails) ? email.cc_emails.join(', ') : email.cc_emails || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.bcc_emails') }}:</div>
								<div class="detail-value">{{ Array.isArray(email.bcc_emails) ? email.bcc_emails.join(', ') : email.bcc_emails || $t('common.no_data') }}</div>
							</div>
							<div v-if="email.conditions && email.conditions.length" class="detail-item">
								<div class="detail-label">{{ $t('workflow.conditions') }}:</div>
								<div class="detail-value">
									<div v-for="(condition, ecIdx) in email.conditions" :key="`ec-${ecIdx}`" class="mb-1">
										{{ condition.name }} ({{ condition.status ? $t('workflow.enabled') : $t('workflow.disabled') }})
									</div>
								</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.content') }}:</div>
								<div class="detail-value email-content">
									<div v-html="email.content || '-'"></div>
								</div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">{{ $t('common.no_data') }}</div>
				</div>
			</div>
		</div>

		<div v-else-if="state.currentDetailType === 'sync_groups' && state.currentDetailData">
			<div class="detail-section">
				<div class="detail-header">{{ $t('workflow.sync_groups_info') }}</div>
				<div class="detail-content">
					<div v-if="state.currentDetailData.sync_groups && state.currentDetailData.sync_groups.length">
						<div v-for="(syncGroup, sgIdx) in state.currentDetailData.sync_groups" :key="`dsg-${sgIdx}`" class="sync-group-item mb-3">
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.name') }}:</div>
								<div class="detail-value">{{ syncGroup.name || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.id') }}:</div>
								<div class="detail-value">{{ syncGroup.id || $t('common.no_data') }}</div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">{{ $t('common.no_data') }}</div>
				</div>
			</div>
		</div>

		<div v-else-if="state.currentDetailType === 'back_to_stage' && state.currentDetailData">
			<CForm class="row g-2">
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.name')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.back_to_stage?.name || $t('common.no_data') }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.approver')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ formatApprovers(state.currentDetailData.back_to_stage?.approvers) }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.follower')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ formatFollowers(state.currentDetailData.back_to_stage?.followers) }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.stage.description')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.back_to_stage?.description || $t('common.no_data') }}
					</div>
				</CCol>
			</CForm>
		</div>

		<div v-else-if="state.currentDetailType === 'action_back_to' && state.currentDetailData">
			<FormKit
				ref="dynamicForm"
				type="form"
				:actions="false"
				incomplete-message=" "
				@submit="() => executeAction(state.currentDetailData, 'action_back_to')"
			>
				<Form ref="form" @submit="() => executeAction(state.currentDetailData, 'action_back_to')" :validation-schema="schema(sortedFormFields)">
					<CForm class="row g-2">
						<CCol :md="6" class="mb-3">
							<CFormLabel class="text-secondary">
								{{ $t('workflow.action.name')}}
							</CFormLabel>
							<div class="fw-normal">
								{{ state.currentDetailData.action_back_to?.name || $t('common.no_data') }}
							</div>
						</CCol>
						<CCol :md="6" class="mb-3">
							<CFormLabel class="text-secondary">
								{{ $t('workflow.action.description')}}
							</CFormLabel>
							<div class="fw-normal">
								{{ state.currentDetailData.action_back_to?.description || $t('common.no_data') }}
							</div>
						</CCol>
						<CCol :xs="12" class="mb-3">
							<CFormLabel class="text-secondary">
								{{ $t('workflow.action.comment') }}
							</CFormLabel>
							<CFormTextarea
								id="comment"
								v-model="state.comment"
								:placeholder="$t('workflow.action.comment_desc')"
								:maxlength="500"
								rows="2"
								:invalid="!!state.commentError"
								@input="state.commentError = ''"
							></CFormTextarea>
							<CFormFeedback invalid>
								{{ state.commentError }}
							</CFormFeedback>
						</CCol>

						<!-- Dynamic Form Fields -->
						<div v-if="state.dynamicFormLoading" class="text-center mb-3">
							<Loading />
						</div>
						<div v-else-if="state.showDynamicForm && sortedFormFields.length > 0">
							<CCol :xs="12" class="mb-3">
								<hr>
								<h6 class="text-secondary mb-3">{{ $t('form.field.setup') }}</h6>
								<DynamicFormFields
									:form-fields="sortedFormFields"
									:form-data="state.formData"
									:item-childrens="state.itemChildrens"
									:select-option-departments="state.selectOptionDepartments"
									:sub-column-table-description="state.subColumnTableDescription"
									:sub-column-table-option-selected="state.subColumnTableOptionSelected"
									:sub-column-table-description-children="state.subColumnTableDescriptionChildren"
									:sub-column-table-option-selected-children="state.subColumnTableOptionSelectedChildren"
									:formatted-formula-results="formattedFormulaResults"
									:max-files="state.maxFiles"
									:max-file-size="state.maxFileSize"
									:accepted-file-types="state.acceptedFileTypes"
									:get-option-users="getOptionUsersHandler"
									:get-option-column-data="getOptionColumnDataHandler"
									@update-form-data="(name, value) => state.formData[name] = value"
									@add-item="(field) => addItem(field)"
									@remove-item="(field, index) => removeItem(field, index)"
									@show-sub-column-table="(optionSelected, keyName) => showSubColumnTable(optionSelected, keyName)"
									@show-sub-column-table-children="(optionSelected, itemIndex, keyName) => showSubColumnTableChildren(optionSelected, itemIndex, keyName)"
									@update-files="(fileItemUploads, fieldName) => updateFiles(fileItemUploads, fieldName)"
									@update-file-childrens="(fileItemUploads, itemChildren, fieldChildrenName) => updateFileChildrens(fileItemUploads, itemChildren, fieldChildrenName)"
								/>
							</CCol>
						</div>

						<CCardFooter>
							<div class="d-flex justify-content-end">
								<CButton
									type="button"
									class="btn btn-light border m-1"
									@click="closeShowDetailModal"
								>
									<span class="text-uppercase">
										{{ $t('workflow.action.close') }}
									</span>
								</CButton>
								<CButton
									type="button"
									class="btn btn-primary m-1"
									@click="executeAction(state.currentDetailData, 'action_back_to')"
								>
									<span class="text-uppercase">
										{{ $t('workflow.action.execute') }}
									</span>
								</CButton>
							</div>
						</CCardFooter>
					</CForm>
				</Form>
			</FormKit>
		</div>

		<div v-else-if="state.currentDetailType === 'email_template_back' && state.currentDetailData">
			<div class="detail-section">
				<div class="detail-header">{{ $t('workflow.back_email_info') }}</div>
				<div class="detail-content">
					<div v-if="state.currentDetailData.email_template_back">
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.template_name') }}:</div>
							<div class="detail-value">{{ state.currentDetailData.email_template_back?.name || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.subject') }}:</div>
							<div class="detail-value">{{ state.currentDetailData.email_template_back?.subject || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.from_email') }}:</div>
							<div class="detail-value">{{ state.currentDetailData.email_template_back?.from_email || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.to_emails') }}:</div>
							<div class="detail-value">{{ Array.isArray(state.currentDetailData.email_template_back?.to_emails) ? state.currentDetailData.email_template_back?.to_emails.join(', ') : state.currentDetailData.email_template_back?.to_emails || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.cc_emails') }}:</div>
							<div class="detail-value">{{ Array.isArray(state.currentDetailData.email_template_back?.cc_emails) ? state.currentDetailData.email_template_back?.cc_emails.join(', ') : state.currentDetailData.email_template_back?.cc_emails || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.bcc_emails') }}:</div>
							<div class="detail-value">{{ Array.isArray(state.currentDetailData.email_template_back?.bcc_emails) ? state.currentDetailData.email_template_back?.bcc_emails.join(', ') : state.currentDetailData.email_template_back?.bcc_emails || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.content') }}:</div>
							<div class="detail-value email-content">
								<div v-html="state.currentDetailData.email_template_back?.content || '-'"></div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">{{ $t('common.no_data') }}</div>
				</div>
			</div>
		</div>

		<div v-else-if="state.currentDetailType === 'process_transition' && state.currentDetailData">
			<div class="detail-section">
				<div class="detail-header">{{ $t('workflow.next_process_info') }}</div>
				<div class="detail-content">
					<div v-if="state.currentDetailData.process_transition && state.currentDetailData.process_transition.length">
						<div v-for="(pt, ptIdx) in state.currentDetailData.process_transition" :key="`dpt-${ptIdx}`" class="process-transition-item mb-4">
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.name') }}:</div>
								<div class="detail-value">{{ pt.name || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.to_process_id') }}:</div>
								<div class="detail-value">{{ pt.to_process_id || $t('common.no_data') }}</div>
							</div>
							<div v-if="pt.conditions && pt.conditions.length" class="detail-item">
								<div class="detail-label">{{ $t('workflow.conditions') }}:</div>
								<div class="detail-value">
									<div v-for="(condition, ptcIdx) in pt.conditions" :key="`ptc-${ptcIdx}`" class="mb-1">
										{{ condition.name }} ({{ condition.status ? $t('workflow.enabled') : $t('workflow.disabled') }})
									</div>
								</div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">{{ $t('common.no_data') }}</div>
				</div>
			</div>
		</div>

		<div v-else class="no-detail-data">
			{{ $t('workflow.no_detail_available') }}
		</div>

		<template #footer>
			<CButton color="secondary" @click="closeShowDetailModal">
				{{ $t('common.close') }}
			</CButton>
		</template>
	</BModal>

	<loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive, computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import useJobs from '@/composables/job';
import useFiles from '@/composables/files';
import useForms from '@/composables/form';
import useDynamicFormState from '@/composables/useDynamicFormState';
import useDynamicFormLogic from '@/composables/useDynamicFormLogic';
import useFormValidation from '@/composables/useFormValidation';
import useOptionsHandlers from '@/composables/useOptionsHandlers';
import { useI18n } from "vue-i18n";
import { TAB_JOB_DETAIL } from '@/constants/constants';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { languageEventBus } from '@/utils/languageUtils';
import { PROCESS_STAGE_STATUS, WORKFLOWS, SAVE_JOB_STATUS } from '@/constants/constants';
import { useToast } from 'vue-toast-notification';
import Loading from '@/views/loading/Loading.vue'
import DynamicFormFields from '@/components/DynamicFormFields.vue'
import moment from 'moment';

// Import FormKit
import { FormKit } from '@formkit/vue';

export default defineComponent({
    name: "JobDetail",

	components: {
        Form,
		Field,
		ErrorMessage,
		Loading,
		DynamicFormFields,
		FormKit,
    },
  
    setup() {
        const route = useRoute();
		const { t }  = useI18n();
		const $toast = useToast();

        const {
			setIsLoading,
			showDetailJob,
			jobDetail,
			updateExecuteAction,
			updateExecuteActionBackTo,
		} = useJobs();

		const {
			getFileName,
			getFileUrl,
			isImageFile,
			isPdfFile,
			isPreviewableFile,
			downloadFile
		} = useFiles();

		const { getFieldsByFormId } = useForms();

		// Use shared composables for dynamic forms
		const {
			createDynamicFormState,
			useSortedFormFields,
			useFormulaCalculations
		} = useDynamicFormState();

		const {
			getOptionDepartments: getOptionDepartmentsHandler,
			getOptionUsers: getOptionUsersHandler,
			getOptionColumnData: getOptionColumnDataHandler
		} = useOptionsHandlers();

		const {
			createDynamicSchema,
			validateRequiredFields
		} = useFormValidation(t);

		const {
			converFormFields,
			initializeValues: initializeValuesFromComposable,
			addItem: addItemFromComposable,
			removeItem: removeItemFromComposable,
			showSubColumnTable: showSubColumnTableFromComposable,
			showSubColumnTableChildren: showSubColumnTableChildrenFromComposable,
			updateFiles: updateFilesFromComposable,
			updateFileChildrens: updateFileChildrensFromComposable
		} = useDynamicFormLogic();

		// Create dynamic form state
		const dynamicFormState = createDynamicFormState();

		const state = reactive({
			showFileModal: false,
			currentFile: '',
			currentFileName: '',
			tabActived: TAB_JOB_DETAIL.DETAIL,
			showDetailModal: false,
			currentDetailType: '',
			currentDetailData: null as any,
			detailModalTitle: '',
			comment: '',
			commentError: '',
			// Add dynamic form state
			...dynamicFormState,
			showDynamicForm: false,
			dynamicFormLoading: false,
		});

		// Create sorted form fields computed with safety check
		const sortedFormFields = computed(() => {
			if (!state.dataFormFields || !Array.isArray(state.dataFormFields)) {
				return [];
			}
			return useSortedFormFields(state.dataFormFields).value;
		});

		// Create formula calculations with safety check
		const { formattedFormulaResults } = useFormulaCalculations(sortedFormFields, state.formData, state.itemChildrens);

		// Create validation schema with safety check
		const schema = (fields: any[]) => {
			if (!fields || !Array.isArray(fields)) {
				return createDynamicSchema([]);
			}
			return createDynamicSchema(fields);
		};

		const dynamicForm: any = ref(null);
		
		// Sử dụng computed để optionTabs cập nhật khi ngôn ngữ thay đổi
		const optionTabs = computed(() => [
            { value: TAB_JOB_DETAIL.DETAIL, title: t('option_tab_job_detail.detail') },
			{ value: TAB_JOB_DETAIL.FILES, title: t('option_tab_job_detail.files') },
		]);

		const getFileList = (fieldValue: any): Array<any> => {
			const files = fieldValue.extracted_values?.length 
				? fieldValue.extracted_values 
				: fieldValue.field_value;

			if (!files) return [];
			return Array.isArray(files) ? files : [files];
		};

		const getFieldValue = (fieldValue: any): string => {
			if (fieldValue.field?.type === 'FILEUPLOAD') {
				return '';
			}

			// Xử lý trường hợp OBJECTSYSTEM
			if (fieldValue.field?.type === 'OBJECTSYSTEM') {
				const values = fieldValue.extracted_values?.length 
					? fieldValue.extracted_values 
					: fieldValue.field_value;
				
				if (Array.isArray(values)) {
					// Nếu là mảng các đối tượng có thuộc tính main
					if (values.length > 0 && typeof values[0] === 'object') {
						return values
							.map(item => item.main || '')
							.filter(Boolean)
							.join(', ');
					}
					// Nếu là mảng các giá trị chuỗi
					return values.join(', ');
				} else if (typeof values === 'object' && values !== null) {
					// Nếu là đối tượng có thuộc tính main (chỉ áp dụng cho field_value)
					return values.main || t('common.no_data');
				} else if (values) {
					return values.toString();
				}
			}

			// Xử lý cho các trường hợp khác
			const values = fieldValue.extracted_values?.length 
				? fieldValue.extracted_values 
				: fieldValue.field_value;
			
			if (!values) {
				return t('common.no_data');
			}
			
			if (Array.isArray(values)) {
				// Nếu là mảng các đối tượng có thuộc tính main
				if (values.length > 0 && typeof values[0] === 'object') {
					return values
						.map(item => item.main || '')
						.filter(Boolean)
						.join(', ');
				}
				// Nếu là mảng các giá trị chuỗi
				return values.join(', ');
			} else if (typeof values === 'object' && values !== null) {
				// Nếu là đối tượng có thuộc tính main (chỉ áp dụng cho field_value)
				return values.main || t('common.no_data');
			}
			
			return values.toString();
		};

		const isActiveTab = (valueTabActived: string) => {
			return state.tabActived === valueTabActived;
		};

		const handleClickTab = (valueTabActived: string) => {
			state.tabActived = valueTabActived;
		};

		const handleFileClick = (file: string) => {
			state.currentFile = file;
			state.currentFileName = getFileName(file);
			state.showFileModal = true;
		};

		const formatTableValue = (value: any): string => {
			if (Array.isArray(value)) {
				// Nếu là mảng các đối tượng có thuộc tính main
				if (value.length > 0 && typeof value[0] === 'object') {
					return value
						.map(item => item.main || '')
						.filter(Boolean)
						.join(', ');
				}
				return value.join(', ');
			} else if (typeof value === 'object' && value !== null) {
				// Nếu là đối tượng có thuộc tính main
				return value.main || t('common.no_data');
			}
			return value || t('common.no_data');
		};

		const isColumnTypeFileUpload = (fieldValue: any, columnKey: string | number): boolean => {
			// Kiểm tra xem có table_columns_type không
			if (!fieldValue.table_columns_type) return false;
			
			// Chuyển key thành chuỗi để đảm bảo
			const key = columnKey.toString();
			
			// Tìm loại cột dựa vào key
			const columnType = fieldValue.table_columns_type[key];
			
			// Kiểm tra xem cột có phải loại FILEUPLOAD không
			return columnType === 'FILEUPLOAD';
		};

		const getFileArrayFromValue = (value: any): string[] => {
			if (!value) return [];
			
			if (typeof value === 'string') {
				return [value];
			}
			
			if (Array.isArray(value)) {
				// Đảm bảo chỉ trả về các phần tử là chuỗi
				return value.filter(item => typeof item === 'string') as string[];
			}
			
			return [];
		};

		// Hàm xử lý và hiển thị thông tin người phê duyệt
		const formatApprovers = (approvers: any) => {
			if (!approvers) return t('common.no_data');
			
			return  Array.isArray(approvers) ? approvers.join(', ') : approvers || t('common.no_data');
		};
		
		// Hàm xử lý và hiển thị thông tin người theo dõi
		const formatFollowers = (followers: any): string => {
			if (!followers) return t('common.no_data');

			return  Array.isArray(followers) ? followers.join(', ') : followers || t('common.no_data');
		};

		const showDetailModal = (type: string, data: any) => {
			state.currentDetailType = type;
			state.currentDetailData = data;
			switch (type) {
				case 'from_stage':
				case 'to_stage':
				case 'back_to_stage':
					state.detailModalTitle = t(`workflow.feature.stage_title`);
					break;
				case 'action':
				case 'action_back_to':
					state.detailModalTitle = t(`workflow.feature.action_title`);
					break;
				case 'email_templates':
				case 'email_template_back':
					state.detailModalTitle = t(`workflow.feature.email_title`);
					break;
				case 'sync_groups':
					state.detailModalTitle = t(`workflow.feature.sync_group_title`);
					break;
				case 'conditions':
					state.detailModalTitle = t(`workflow.feature.condition_title`);
					break;
				case 'process_transition':
					state.detailModalTitle = t(`workflow.feature.process_transition_title`);
					break;
				default:
					break;
			}
			state.showDetailModal = true;
		};

		const closeShowDetailModal = () => {
			state.showDetailModal = false;
			state.currentDetailData = null;
			state.comment = '';
			state.commentError = '';
			// Reset dynamic form state
			state.showDynamicForm = false;
			state.dataFormFields = [];
			state.formData = {};
			state.itemChildrens = {};
		}

		// Load dynamic form fields
		const loadDynamicFormFields = async (data: any) => {
			if (!jobDetail.value?.process_version?.process?.form_id || !data.from_stage?.id) {
				return;
			}

			state.dynamicFormLoading = true;
			try {
				const formId = jobDetail.value.process_version.process.form_id;
				const stageId = data.from_stage.id;

				state.dataFormFields = await getFieldsByFormId(formId, stageId);

				// Convert form fields
				if (state.dataFormFields && state.dataFormFields.length > 0) {
					state.dataFormFields = converFormFields(state.dataFormFields, false);

					// Initialize form data and item childrens
					const sortedFields = state.dataFormFields.filter((item: any) => item.parent_id === null).sort((a: any, b: any) => a.order - b.order);
					initializeValuesFromComposable(sortedFields, state.formData);

					// Initialize item childrens for TABLE fields
					sortedFields.forEach((field: any) => {
						if (field.type === 'TABLE') {
							if (!state.itemChildrens[field.name]) {
								state.itemChildrens[field.name] = [];
							}
						}
					});

					state.showDynamicForm = true;
				}
			} catch (error) {
				console.error('Error loading dynamic form fields:', error);
			} finally {
				state.dynamicFormLoading = false;
			}
		};

		const executeAction = async (data: any, type: string) => {

			if (!data) return;

			state.commentError = '';

			if (data.from_stage?.comment && !state.comment.trim()) {
				state.commentError = t('workflow.action.comment_desc');
				return;
			}

			// Load dynamic form if needed
			if (!state.showDynamicForm) {
				console.log('Load dynamic form');
				
				await loadDynamicFormFields(data);
				return; // Show form first, actual execution will happen on form submit
			}

			// Validate dynamic form fields if they exist
			if (state.showDynamicForm && sortedFormFields.value.length > 0) {
				const validationErrors = validateRequiredFields(sortedFormFields.value, state.formData, state.itemChildrens);
				if (validationErrors.length > 0) {
					const combinedMessage = validationErrors.join('<br>');
					$toast.open({
						message: combinedMessage,
						type: "warning",
						duration: 10000,
						dismissible: true,
						position: "bottom-right",
					});
					return;
				}

				// Validate FormKit form
				const node = dynamicForm.value?.node;
				const isValidFormKit = node && node.context?.state?.valid;
				if (!isValidFormKit) {
					$toast.open({
						message: t('toast.error_code.INVALID_FORM_DATA'),
						type: "warning",
						duration: 5000,
						dismissible: true,
						position: "bottom-right",
					});
					return;
				}
			}


			let result: any;
			const actionId = data.action?.id;
			const stageId = data.from_stage?.id;
			const conditionIds = data.conditions?.map((condition: any) => condition.id) || [];
			const emailConditionIds = data.email_templates?.flatMap((email: any) => email.conditions?.map((condition: any) => condition.id) || []) || [];
			const pendingApprovalId = jobDetail.value.pending_approval.find((item: any) => item.stage_id === stageId)?.id;

			if (type === 'action') {
				result = await updateExecuteAction(
					route.params.id as string,
					actionId,
					stageId,
					state.comment,
					pendingApprovalId,
					conditionIds,
					emailConditionIds
				);
			} else {
				const actionBackToId = data.action_back_to?.id;
				const backToStageId = data.back_to_stage?.id;
				const emailConditionIdsBackTo = data.email_template_back?.flatMap((email: any) => email.conditions?.map((condition: any) => condition.id) || []) || [];
				result = await updateExecuteActionBackTo(
					route.params.id as string,
					actionBackToId,
					stageId,
					backToStageId,
					state.comment,
					pendingApprovalId,
					emailConditionIdsBackTo
				);
			}

			if (result.status === 'success') {
				refreshJobData();
				closeShowDetailModal();
				$toast.open({
					message: t('toast.status.ACTION_SUCCESS'),
					type: "success",
					duration: 5000,
					dismissible: true,
					position: "bottom-right",
				});
			}
		}

		const refreshJobData = async () => {
			await showDetailJob(route.params.id as string);
		};

		// Helper functions for DynamicFormFields
		const addItem = (field: any) => {
			addItemFromComposable(field, state.itemChildrens);
		};

		const removeItem = (field: any, itemIndex: number) => {
			removeItemFromComposable(field, itemIndex, state.itemChildrens);
		};

		const showSubColumnTable = (optionSelected: any, keyName: string) => {
			showSubColumnTableFromComposable(optionSelected, keyName, state.subColumnTableDescription, state.subColumnTableOptionSelected);
		};

		const showSubColumnTableChildren = (optionSelected: any, itemIndex: number, keyName: string) => {
			showSubColumnTableChildrenFromComposable(
				optionSelected,
				itemIndex,
				keyName,
				state.subColumnTableDescriptionChildren,
				state.subColumnTableOptionSelectedChildren
			);
		};

		const updateFiles = (fileItemUploads: any, fieldName: string) => {
			updateFilesFromComposable(fileItemUploads, fieldName, state.formData);
		};

		const updateFileChildrens = (fileItemUploads: any, itemChildren: any, fieldChildrenName: string) => {
			updateFileChildrensFromComposable(fileItemUploads, itemChildren, fieldChildrenName);
		};

		// Helper methods cho history timeline
		const getHistoryActorName = (history: any): string => {
			if (history.actor && history.actor.full_name) {
				return history.actor.full_name;
			}

			return t('common.unknown');
		};

		const getHistoryAction = (history: any): any => {
			let actionText = '';
			let actionIcon = '';
			let actionIconClass = '';
			if (!!history.action) {
				actionText = `${t('job.history.action_updated')}`;
				actionIcon = 'check';
				actionIconClass = 'bg-success';
			} else {
				if (history.action_id === WORKFLOWS.ACTION.CREATE) {
					actionText = t('job.history.created_new_job');
					actionIcon = 'add';
					actionIconClass = 'bg-primary';
				}
				if (history.action_id === WORKFLOWS.ACTION.BACK_TO) {
					actionText = t('job.history.back_to_stage');
					actionIcon = 'undo';
					actionIconClass = 'bg-warning';
				}
			}

			return {
				actionText,
				actionIcon,
				actionIconClass
			};
		};

		const formatHistoryDate = (dateString: string): string => {
			if (!dateString) return '';

			try {
				const date = new Date(dateString);
				const now = new Date();
				const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

				if (diffInMinutes < 1) {
					return t('time.just_now');
				} else if (diffInMinutes < 60) {
					return t('time.minutes_ago', { minutes: diffInMinutes });
				} else if (diffInMinutes < 1440) { // 24 hours
					const hours = Math.floor(diffInMinutes / 60);
					return t('time.hours_ago', { hours });
				} else {
					// Hiển thị ngày tháng đầy đủ
					return date.toLocaleString('vi-VN', {
						year: 'numeric',
						month: '2-digit',
						day: '2-digit',
						hour: '2-digit',
						minute: '2-digit'
					});
				}
			} catch (error) {
				return dateString;
			}
		};

		const getStatusBadge = (status: string) => {
            if (!status) {
                return { text: t('workflow.stage.status.pending'), color: 'primary' };
            }
            const statusKey = `workflow.stage.status.${status.toLowerCase()}`;

            const statusText = t(statusKey, status);

            let color = 'secondary';
            switch (status.toLowerCase()) {
                case PROCESS_STAGE_STATUS.PROCESSING:
                    color = 'warning';
                    break;
                case PROCESS_STAGE_STATUS.COMPLETED:
                    color = 'success';
                    break;
                case PROCESS_STAGE_STATUS.PENDING:
                    color = 'primary';
                    break;
                case PROCESS_STAGE_STATUS.CANCEL:
                    color = 'danger';
                    break;
            }

			return { text: statusText, color };
		};

		// Handle language change
		const handleLanguageChange = (newLang: string) => {
			refreshJobData();
		};

		// Register language change listener
		onMounted(async () => {
			await showDetailJob(route.params.id as string);
			// Add listener for language changes
			languageEventBus.onLanguageChanged(handleLanguageChange);
		});

		// Clean up language change listener
		onUnmounted(() => {
			// Remove the listener to prevent memory leaks
			const index = languageEventBus.listeners.findIndex(listener => listener === handleLanguageChange);
			if (index !== -1) {
				languageEventBus.listeners.splice(index, 1);
			}
		});

		const filteredHistories = computed(() => {
			return jobDetail.value?.job_approval_histories?.filter((history: any) => history.user_id);
		});

		const formatDateTime = (date: string) => {
            return moment(date).format('HH:mm DD/MM/YYYY ');
        };

        return {
			setIsLoading,
			TAB_JOB_DETAIL,
			WORKFLOWS,
			SAVE_JOB_STATUS,
            state,
			optionTabs,
			isActiveTab,
			handleClickTab,
			jobDetail,
			getFieldValue,
			getFileList,
			handleFileClick,
			getFileName,
			isPreviewableFile,
			isImageFile,
			isPdfFile,
			downloadFile,
			getFileUrl,
			formatTableValue,
			isColumnTypeFileUpload,
			getFileArrayFromValue,
			formatApprovers,
			formatFollowers,
			showDetailModal,
			closeShowDetailModal,
			executeAction,
			getStatusBadge,
			getHistoryActorName,
			getHistoryAction,
			formatHistoryDate,
			filteredHistories,
			formatDateTime,
			// Dynamic form related
			sortedFormFields,
			formattedFormulaResults,
			schema,
			dynamicForm,
			addItem,
			removeItem,
			showSubColumnTable,
			showSubColumnTableChildren,
			updateFiles,
			updateFileChildrens,
			// Option handlers
			getOptionUsersHandler,
			getOptionColumnDataHandler,
        }
    }
});
</script>

<style scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color:#f8f9fa;
    padding: 10px;
}
.object-field-item {
	display: inline-block;
	min-width: 200px;
	white-space: normal;
	word-wrap: break-word;
	padding: 0 10px;
}

/* History Timeline Styles */
.history-timeline {
	position: relative;
}

.history-item {
	position: relative;
}

.history-icon-wrapper {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 40px;
}

.history-icon {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background-color: #fff;
	border: 2px solid #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
}

.history-line {
	width: 2px;
	background-color: #e9ecef;
	flex-grow: 1;
	min-height: 20px;
	margin-top: 8px;
}

.history-content {
	padding-left: 8px;
	padding-bottom: 8px;
}

.history-header {
	margin-bottom: 4px;
}

.history-actor {
	color: #495057;
	font-size: 14px;
}

.history-action {
	font-size: 14px;
}

.history-stage {
	font-size: 12px;
}

.history-time {
	font-size: 12px;
	white-space: nowrap;
}

.history-stage {
	background-color: #f8f9fa;
	border-left: 3px solid #007bff;
	padding: 8px 12px;
	border-radius: 4px;
	font-size: 12px;
	color: #495057;
}

.comment-text {
	line-height: 1.4;
}

.object-main-title {
	display: inline-block;
	min-width: 250px;
	white-space: normal;
	word-wrap: break-word;
	padding-right: 15px;
}

.custom-table {
	table-layout: auto;
	width: 100%;
}

.custom-table th, .custom-table td {
	white-space: normal;
	word-wrap: break-word;
	overflow-wrap: break-word;
	padding: 8px;
}

/* Loại bỏ giới hạn độ rộng cho cột OBJECTSYSTEM */
.custom-table th[data-object-system], 
.custom-table td[data-object-system] {
	width: auto;
}

/* Đảm bảo các cột tự động điều chỉnh theo nội dung */
.custom-table th {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* CSS cho bảng workflow */
.workflow-table {
	border-collapse: collapse;
	width: 100%;
	font-size: 0.9rem;
}

.workflow-table th {
	background-color: #f8f9fa;
	vertical-align: middle;
	padding: 10px;
	white-space: nowrap;
}

.workflow-table td {
	padding: 8px;
	vertical-align: top;
	min-width: 200px;
}

.workflow-table td.has-data {
	cursor: pointer;
	position: relative;
}

.workflow-table td.has-data:hover {
	background-color: #e9f5ff;
}

.workflow-table td.has-data::after {
	content: "";
	position: absolute;
	bottom: 3px;
	right: 3px;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background-color: #0d6efd;
}

.workflow-table td.not-executable {
	cursor: not-allowed;
	opacity: 0.6;
	background-color: #f5f5f5;
}

.workflow-table td.not-executable:hover {
	background-color: #f5f5f5 !important;
}

.workflow-table td.not-executable.has-data::after {
	background-color: #dc3545;
}

.workflow-table tr:hover {
	background-color: #f5f5f5;
}

.workflow-table tr:nth-child(even) {
	background-color: #fafafa;
}

.table-responsive {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>